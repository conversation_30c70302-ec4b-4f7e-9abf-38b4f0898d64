import { defineComponent, PropType, provide, VNode } from 'vue';
import HierarchyItem from './hierarchy-item';
import { Checkbox } from 'ant-design-vue';

export type TableColumn = Partial<{
  title: string | () => VNode;
  key: string;
  dataIndex: string;
  customRender: renderFunction;
  customHeaderCell: customRenderFunction;
  scopedSlots: Record<string, string>;
  width: number | string;
  align: 'left' | 'right' | 'center';
  fixed: boolean | string;
  sorter: boolean;
  [key: string]: any;
}>;

const HierarchyTable = defineComponent({
  name: 'HierarchyTable',
  props: {
    rowKey: {
      type: String,
      default: 'id',
    },
    list: {
      type: Array,
      default: () => [],
    },
    // 是否默认展开所有项
    defaultExpandAll: {
      type: Boolean,
      default: false,
    },
    // 是否可折叠
    collapsible: {
      type: Boolean,
      default: true,
    },
    selectable: {
      type: Boolean,
      default: false,
    },
    columns: {
      type: Array as PropType<TableColumn[]>,
      default: () => [],
    },
  },
  emits: ['expand', 'collapse'],
  setup(props, { emit }) {
    provide('selectable', props.selectable);

    const handleExpand = (data: any) => {
      emit('expand', data);
    };
    const handleCollapse = (data: any) => {
      emit('collapse', data);
    };

    const checkboxChange = (values) => {
      console.log(values);
    };

    return {
      handleCollapse,
      handleExpand,
      checkboxChange,
    };
  },
  render() {
    const { list, defaultExpandAll, collapsible, rowKey, column } = this;
    const { header, body } = this.$scopedSlots;

    const renderTh = () => {
      return (
        <div>
          {this.columns.map((column) => {
            return <div>{column.title}</div>;
          })}
        </div>
      );
    };

    return (
      <Checkbox.Group onChange={this.checkboxChange} class="w-full">
        {(list as any[]).map((item: any, index: number) => {
          return (
            <HierarchyItem
              key={item[rowKey]}
              data={item}
              defaultExpanded={defaultExpandAll}
              collapsible={collapsible}
              onExpand={this.handleExpand}
              onCollapse={this.handleCollapse}
              scopedSlots={this.$scopedSlots}
              selectable={this.selectable}
              value={item[rowKey]}
            >
              {header ? <template slot="header">{header(item)}</template> : null}
              {body ? <template slot="default">{body(item)}</template> : null}
            </HierarchyItem>
          );
        })}
      </Checkbox.Group>
    );
  },
});

export default HierarchyTable;
